"""
工作流数据持久化管理器
负责保存和恢复上一次成功的工作流数据，支持 --again 功能

功能描述: 管理工作流数据的持久化存储，支持保存完整的工作流配置和快速重新生成
"""

import json
import os
import time
import logging
from typing import Dict, Any, Optional, List
from pathlib import Path
from dataclasses import dataclass, asdict
from datetime import datetime


@dataclass
class WorkflowSnapshot:
    """工作流快照数据"""
    workflow_data: Dict[str, Any]      # 完整的工作流JSON数据
    user_prompt: str                   # 用户原始提示词
    optimized_prompt: str              # 优化后的英文提示词
    workflow_type: str                 # 工作流类型 (flux_default, flux_controlnet等)
    parameters: Dict[str, Any]         # 工作流参数
    lora_info: Dict[str, Any]          # LoRA模型信息
    image_info: Dict[str, Any]         # 图片信息（如果有）
    timestamp: float                   # 保存时间戳
    user_id: str                       # 用户ID
    chat_id: str                       # 聊天ID
    success: bool = True               # 是否成功生成


class WorkflowPersistenceManager:
    """工作流持久化管理器"""
    
    def __init__(self, storage_dir: Optional[str] = None):
        # 在Docker环境中，使用当前工作目录或环境变量来确定正确的路径
        if storage_dir is None:
            if os.path.exists('/app'):
                # Docker容器环境，使用/app作为根目录
                storage_dir = '/app/temp'
            else:
                # 本地开发环境，使用项目根目录
                storage_dir = "temp"
        
        self.storage_dir = Path(storage_dir)
        self.storage_dir.mkdir(exist_ok=True)
        
        # 🔥 修复：不再使用固定文件名，改为基于用户ID的动态文件名
        # 存储文件路径将在保存/加载时动态生成
        
        self.logger = logging.getLogger(__name__)
        
        # 最大历史记录数量
        self.max_history = 10

    def _get_user_workflow_file(self, user_id: str) -> Path:
        """
        获取用户专用的工作流文件路径

        Args:
            user_id: 用户ID

        Returns:
            Path: 用户专用的工作流文件路径
        """
        # 🔥 修复：基于用户ID创建独立的文件，确保文件名安全
        import hashlib

        # 对于安全性，使用用户ID的哈希值作为文件名的一部分
        user_hash = hashlib.md5(user_id.encode('utf-8')).hexdigest()[:8]

        # 提取用户ID中的安全字符
        safe_chars = "".join(c for c in user_id if c.isalnum() or c in "._-")[:20]

        # 组合安全的文件名：safe_chars + hash
        safe_filename = f"{safe_chars}_{user_hash}" if safe_chars else user_hash

        return self.storage_dir / f"last_workflow_{safe_filename}.json"

    def _get_user_history_file(self, user_id: str) -> Path:
        """
        获取用户专用的历史记录文件路径

        Args:
            user_id: 用户ID

        Returns:
            Path: 用户专用的历史记录文件路径
        """
        import hashlib

        # 使用与工作流文件相同的安全命名策略
        user_hash = hashlib.md5(user_id.encode('utf-8')).hexdigest()[:8]
        safe_chars = "".join(c for c in user_id if c.isalnum() or c in "._-")[:20]
        safe_filename = f"{safe_chars}_{user_hash}" if safe_chars else user_hash

        return self.storage_dir / f"workflow_history_{safe_filename}.json"
    
    def save_successful_workflow(
        self,
        workflow_data: Dict[str, Any],
        user_prompt: str,
        optimized_prompt: str,
        workflow_type: str,
        parameters: Dict[str, Any],
        lora_info: Dict[str, Any],
        image_info: Dict[str, Any],
        user_id: str,
        chat_id: str = ""
    ) -> bool:
        """
        保存成功的工作流数据
        
        Args:
            workflow_data: 完整的工作流JSON数据
            user_prompt: 用户原始提示词
            optimized_prompt: 优化后的英文提示词
            workflow_type: 工作流类型
            parameters: 工作流参数
            lora_info: LoRA模型信息
            image_info: 图片信息
            user_id: 用户ID
            chat_id: 聊天ID
            
        Returns:
            bool: 是否保存成功
        """
        try:
            # 创建工作流快照
            snapshot = WorkflowSnapshot(
                workflow_data=workflow_data,
                user_prompt=user_prompt,
                optimized_prompt=optimized_prompt,
                workflow_type=workflow_type,
                parameters=parameters,
                lora_info=lora_info,
                image_info=image_info,
                timestamp=time.time(),
                user_id=user_id,
                chat_id=chat_id,
                success=True
            )
            
            # 🔥 修复：保存到用户专用文件
            user_workflow_file = self._get_user_workflow_file(user_id)
            with open(user_workflow_file, 'w', encoding='utf-8') as f:
                json.dump(asdict(snapshot), f, ensure_ascii=False, indent=2)

            # 添加到用户专用历史记录
            self._add_to_user_history(snapshot, user_id)
            
            self.logger.info(f"✅ 工作流数据已保存 - 用户: {user_id}, 类型: {workflow_type}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 保存工作流数据失败: {e}")
            return False
    
    def load_last_successful_workflow(self, user_id: str, chat_id: str = "") -> Optional[WorkflowSnapshot]:
        """
        加载上一次成功的工作流数据
        
        Args:
            user_id: 用户ID
            chat_id: 聊天ID
            
        Returns:
            WorkflowSnapshot: 工作流快照，如果没有则返回None
        """
        try:
            # 🔥 修复：从用户专用文件加载
            user_workflow_file = self._get_user_workflow_file(user_id)
            if not user_workflow_file.exists():
                self.logger.warning(f"没有找到用户 {user_id} 的工作流数据")
                return None

            with open(user_workflow_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            snapshot = WorkflowSnapshot(**data)

            # 🔥 修复：不再需要检查用户ID，因为文件本身就是用户专用的
            # 但为了安全起见，仍然验证一下
            if snapshot.user_id != user_id:
                self.logger.warning(f"文件内用户ID不匹配: {snapshot.user_id} != {user_id}")
                return None
            
            # 检查数据是否过期（24小时）
            if time.time() - snapshot.timestamp > 24 * 3600:
                self.logger.warning("工作流数据已过期（超过24小时）")
                return None
            
            self.logger.info(f"✅ 加载上一次工作流数据 - 用户: {user_id}, 类型: {snapshot.workflow_type}")
            return snapshot
            
        except Exception as e:
            self.logger.error(f"❌ 加载工作流数据失败: {e}")
            return None
    
    def _add_to_user_history(self, snapshot: WorkflowSnapshot, user_id: str):
        """添加到用户专用历史记录"""
        try:
            history = []

            # 🔥 修复：使用用户专用历史文件
            user_history_file = self._get_user_history_file(user_id)

            # 加载现有历史
            if user_history_file.exists():
                with open(user_history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)

            # 添加新记录
            history.append(asdict(snapshot))

            # 保持最大数量限制
            if len(history) > self.max_history:
                history = history[-self.max_history:]

            # 保存历史
            with open(user_history_file, 'w', encoding='utf-8') as f:
                json.dump(history, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.logger.error(f"添加用户历史记录失败: {e}")

    def _add_to_history(self, snapshot: WorkflowSnapshot):
        """添加到历史记录（兼容性方法）"""
        # 重定向到用户专用方法
        self._add_to_user_history(snapshot, snapshot.user_id)
    
    def get_workflow_history(self, user_id: str, limit: int = 5) -> List[WorkflowSnapshot]:
        """
        获取用户的工作流历史记录
        
        Args:
            user_id: 用户ID
            limit: 返回记录数量限制
            
        Returns:
            List[WorkflowSnapshot]: 历史记录列表
        """
        try:
            # 🔥 修复：从用户专用历史文件加载
            user_history_file = self._get_user_history_file(user_id)

            if not user_history_file.exists():
                return []

            with open(user_history_file, 'r', encoding='utf-8') as f:
                history_data = json.load(f)

            # 转换为对象并返回最新的limit条记录
            user_history = []
            for data in reversed(history_data):  # 最新的在前
                user_history.append(WorkflowSnapshot(**data))
                if len(user_history) >= limit:
                    break

            return user_history
            
        except Exception as e:
            self.logger.error(f"获取历史记录失败: {e}")
            return []
    
    def clear_user_data(self, user_id: str) -> bool:
        """
        清理指定用户的数据
        
        Args:
            user_id: 用户ID
            
        Returns:
            bool: 是否清理成功
        """
        try:
            # 🔥 修复：清理用户专用文件
            user_workflow_file = self._get_user_workflow_file(user_id)
            user_history_file = self._get_user_history_file(user_id)

            files_removed = 0

            # 清理最新工作流数据
            if user_workflow_file.exists():
                user_workflow_file.unlink()
                files_removed += 1
                self.logger.info(f"已清理用户 {user_id} 的最新工作流数据")

            # 清理历史记录
            if user_history_file.exists():
                user_history_file.unlink()
                files_removed += 1
                self.logger.info(f"已清理用户 {user_id} 的历史记录")

            if files_removed > 0:
                self.logger.info(f"用户 {user_id} 的数据清理完成，删除了 {files_removed} 个文件")
            else:
                self.logger.info(f"用户 {user_id} 没有需要清理的数据")

            return True
            
        except Exception as e:
            self.logger.error(f"清理用户数据失败: {e}")
            return False
    
    def cleanup_old_data(self, max_age_hours: int = 24) -> int:
        """
        清理过期数据
        
        Args:
            max_age_hours: 最大保留时间（小时）
            
        Returns:
            int: 清理的记录数量
        """
        try:
            cutoff_time = time.time() - (max_age_hours * 3600)
            cleaned_count = 0
            
            # 清理历史记录
            if self.workflow_history_file.exists():
                with open(self.workflow_history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)
                
                # 过滤过期记录
                filtered_history = [h for h in history if h.get('timestamp', 0) > cutoff_time]
                cleaned_count = len(history) - len(filtered_history)
                
                if cleaned_count > 0:
                    with open(self.workflow_history_file, 'w', encoding='utf-8') as f:
                        json.dump(filtered_history, f, ensure_ascii=False, indent=2)
                    
                    self.logger.info(f"清理了 {cleaned_count} 条过期记录")
            
            # 检查最新工作流是否过期
            if self.last_workflow_file.exists():
                with open(self.last_workflow_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                if data.get('timestamp', 0) < cutoff_time:
                    self.last_workflow_file.unlink()
                    self.logger.info("最新工作流数据已过期，已清理")
                    cleaned_count += 1
            
            return cleaned_count
            
        except Exception as e:
            self.logger.error(f"清理过期数据失败: {e}")
            return 0


# 全局单例
_persistence_manager: Optional[WorkflowPersistenceManager] = None

def get_workflow_persistence_manager() -> WorkflowPersistenceManager:
    """获取工作流持久化管理器单例"""
    global _persistence_manager
    if _persistence_manager is None:
        _persistence_manager = WorkflowPersistenceManager()
    return _persistence_manager
